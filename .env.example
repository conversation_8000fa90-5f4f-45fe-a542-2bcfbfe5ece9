# OpenAI Configuration (Required)
OPENAI_API_KEY=
# OPENAI_BASE_URL=https://api.openai.com/v1  # Optional: Custom OpenAI endpoint
# OPENAI_ORGANIZATION=your_org_id_here       # Optional: OpenAI organization ID

# OpenAI Model Configuration (Optional)
OPENAI_MODEL=gpt-4o                          # Model to use (default: gpt-4o)
OPENAI_TEMPERATURE=0.1                       # Response temperature (0-2, default: 0.1)
OPENAI_MAX_TOKENS=4096                       # Maximum tokens per response (default: 4096)
OPENAI_MAX_RETRIES=3                         # Maximum retry attempts (default: 3)
OPENAI_RETRY_DELAY=2000                      # Retry delay in milliseconds (default: 2000)

# Agent Configuration
AGENT_MAX_STEPS=10          # Maximum steps the agent can take
AGENT_TIMEOUT=60000         # Agent timeout in milliseconds (60 seconds)

# Server Manager Configuration
MAX_CONCURRENT_SERVERS=3    # Maximum number of concurrent active servers
SERVER_STARTUP_TIMEOUT=30   # Server startup timeout in seconds

# Logging Configuration
LOG_LEVEL=info              # Log level: debug, info, warn, error
LOG_FORMAT=text             # Log format: text, json
# LOG_FILE=./logs/agent.log # Optional: Log file path

# Development Configuration
NODE_ENV=development

# Example MCP Server Configurations
# These are examples - customize based on your needs
#
# File System Server (stdio)
# Provides file operations
# Command: npx @modelcontextprotocol/server-filesystem /tmp
#
# Web Browser Server (stdio)
# Provides web browsing with Puppeteer
# Command: npx @modelcontextprotocol/server-puppeteer
#
# SQLite Database Server (stdio)
# Provides database operations
# Command: npx @modelcontextprotocol/server-sqlite ./database.db
#
# Memory Server (stdio)
# Provides persistent memory
# Command: npx @modelcontextprotocol/server-memory
#
# Custom HTTP Server Example
# MCP_CUSTOM_HTTP_URL=http://localhost:3001/mcp
# MCP_CUSTOM_HTTP_TOKEN=your_auth_token_here
#
# Custom WebSocket Server Example
# MCP_CUSTOM_WS_URL=ws://localhost:3002/mcp
